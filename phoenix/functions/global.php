<?php
define('REGIONS', [
    'global' => [
        'timezone'      => "Europe/Stockholm",
        'lang'          => "GLOBAL",
        'country'       => "GLOBAL",
        'currency'      => "EUR",
    ],
    'int' => [
        'timezone'      => "Europe/Stockholm",
        'lang'          => "ENGLISH",
        'country'       => "GLOBAL",
        'currency'      => "EUR",
    ],
    'en' => [
        'timezone'      => "Europe/Stockholm",
        'lang'          => "ENGLISH",
        'country'       => "GLOBAL",
        'currency'      => "EUR",
    ],
    'en-us' => [
        'timezone'      => "Europe/Stockholm",
        'lang'          => "ENGLISH",
        'country'       => "UNITED STATES",
        'currency'      => "USD",
    ],
    'sv' => [
        'timezone'      => "Europe/Stockholm",
        'lang'          => "SWEDISH",
        'country'       => "SWEDEN",
        'currency'      => "SEK",
    ],
    'dk' => [
        'timezone'      => "Europe/Copenhagen",
        'lang'          => "DANISH",
        'country'       => "DENMARK",
        'currency'      => "DKK"
    ],
    'da' => [
        'timezone'      => "Europe/Copenhagen",
        'lang'          => "DANISH",
        'country'       => "DENMARK",
        'currency'      => "DKK",
    ],
    'nl' => [
        'timezone'      => "Europe/Amsterdam",
        'lang'          => "DUTCH",
        'country'       => "NETHERLANDS",
        'currency'      => "EUR",
    ],
    'no' => [
        'timezone'      => "Europe/Oslo",
        'lang'          => "NORWEGIAN",
        'country'       => "NORWAY",
        'currency'      => "NOK",
    ],
    'fi' => [
        'timezone'      => "Europe/Helsinki",
        'lang'          => "FINNISH",
        'country'       => "FINLAND",
        'currency'      => "EUR",
    ],
    'pl' => [
        'timezone'      => "Europe/Warsaw",
        'lang'          => "POLISH",
        'country'       => "POLAND",
        'currency'      => "PLN",
    ],
    'de' => [
        'timezone'      => "Europe/Berlin",
        'lang'          => "GERMAN",
        'country'       => "GERMANY",
        'currency'      => "EUR",
    ],
    'es' => [
        'timezone'      => "Europe/Madrid",
        'lang'          => "SPANISH",
        'country'       => "SPAIN",
        'currency'      => "EUR",
    ],
    'ca' => [
        'timezone'      => "America/Toronto",
        'lang'          => "CANADIAN",
        'country'       => "CANADA",
        'currency'      => "CAD",
    ],
    'en-ca' => [
        'timezone'      => "America/Toronto",
        'lang'          => "CANADIAN",
        'country'       => "CANADA",
        'currency'      => "CAD",
    ],
    'on' => [ // Ontario
        'timezone'      => "America/Toronto",
        'lang'          => "CANADIAN_ONTARIO",
        'country'       => "ONTARIO (CANADA)",
        'currency'      => "CAD",
    ],
    'in' => [
        'timezone'      => "Asia/Kolkata",
        'lang'          => "ENGLISH_IN",
        'country'       => "INDIA",
        'currency'      => "INR",
    ],
    'ja' => [
        'timezone'      => "Asia/Tokyo",
        'lang'          => "JAPANESE",
        'country'       => "JAPAN",
        'currency'      => "JPY",
    ],
    'au' => [
        'timezone'      => "",
        'lang'          => "AUSTRALIAN",
        'country'       => "AUSTRALIA",
        'currency'      => "AUD",
    ],
    'nz' => [
        'timezone'      => "Pacific/Auckland",
        'lang'          => "ENGLISH",
        'country'       => "NEW ZEALAND",
        'currency'      => "NZD",
    ],
    'en-nz' => [
        'timezone'      => "Pacific/Auckland",
        'lang'          => "ENGLISH",
        'country'       => "NEW ZEALAND",
        'currency'      => "NZD",
    ],
]);

define('BRAND_REGIONS', [
    'phoenix'        => "sv",
    'casinostugan'   => "sv",
    'casinostuen'    => "dk",
    'cherrycasino'   => "no",
    'comeon'         => "sv",
    'euroslots'      => "fi",
    'folkeriket'     => "no", // sagakingdom
    'galaksino'      => "fi",
    'getlucky'       => "no",
    'hajper'         => "sv",
    'lyllo'          => "sv",
    'mobilautomaten' => "no", // mobilespin
    'mobilebet'      => "de",
    'nopeampi'       => "fi",
    'norgesspill'    => "no",
    'pzbuk'          => "pl",
    'snabbare'       => "sv",
    'sunmaker'       => "de",
    'suomikasino'    => "fi",
    '888'            => "nl"
]);

define('COMPLIANCE_HEADER_REGIONS_FOR_LOGGED_OUT', [
    'sv',  // SGA - Sweden
]);

define('COMPLIANCE_HEADER_REGIONS_FOR_LOGGED_IN', [
    'sv',  // SGA  - Sweden
    'da',  // DGA  - Denmark (old) misspelled language code
    'dk',  // DGA  - Denmark
    'ca',  // AGCO - Canada
    'on',  // AGCO - Canada (ontario)
    'de',  // GGA  - German
    'nl',  // KSA  - Netherlands Gaming Authority / KANSSPELAUTORITEIT
]);

function getCurrentBrand() {
    $themeToUse = get_stylesheet();

    if ( !(DEV_ENV || LOCAL_ENV) )
        return $themeToUse;

    $user_id = get_current_user_id();
    $user_theme = get_transient('px_user_theme_' . $user_id);

    // If there is not personal theme set, use global theme / default
    if (empty($user_theme))
        return $themeToUse;

    $themeToUse = $user_theme;

    // Set WordPress template globals
    global $wp_stylesheet_path, $wp_template_path;
    $theme = wp_get_theme($themeToUse);
    $wp_stylesheet_path = WP_CONTENT_DIR . '/themes/' . $themeToUse;

    // Ensure $theme is a WP_Theme object before calling methods
    if (is_object($theme) && method_exists($theme, 'get_template')) {
        $wp_template_path = WP_CONTENT_DIR . '/themes/' . $theme->get_template();
        $template_name = $theme->get_template();
    } else {
        // Fallback if wp_get_theme returns unexpected result
        $wp_template_path = WP_CONTENT_DIR . '/themes/' . $themeToUse;
        $template_name = $themeToUse;
    }

    // Basic theme filters
    add_filter('stylesheet', function() use ($themeToUse) {
        return $themeToUse;
    });

    add_filter('template', function() use ($template_name) {
        return $template_name;
    });

    return $themeToUse;
}

// Get timezone per brand & FID
function getCurrentTimezone() {
    return REGIONS[CURRENT_REGION]['timezone'] ?? "Europe/Stockholm";
}

// Get region
function getCurrentRegion() {
    // This method is deprecated in WPML, see: https://wpml.org/faq/how-to-get-current-language-with-wpml/
    if (defined('ICL_LANGUAGE_CODE')) {
        if (!empty(ICL_LANGUAGE_CODE)) {
            return ICL_LANGUAGE_CODE;
        }
    }

    // Latest method to get current language in WPML
    $current_lang_code = apply_filters('wpml_current_language', NULL);
    if (!empty($current_lang_code)) {
        return $current_lang_code;
    }

    $locale = explode('_', get_locale());
    $region = strtolower((string) $locale[0]);
    if (!empty($region)) {
        return $region;
    }

    return BRAND_REGIONS[CURRENT_BRAND] ?? "";
}

// Get current language (long)
// Used for pixel tracking
function getCurrentLanguage() {
    return REGIONS[CURRENT_REGION]['lang'] ?? "";
}

// Get country name matching with content language
function getCurrentCountry() {
    return REGIONS[CURRENT_REGION]['country'] ?? "";
}

// Get current brand currency
function getCurrentCurrency() {
    return REGIONS[CURRENT_REGION]['currency'] ?? "";
}

// Get currency sign
// EUR -> €
function getCurrencySymbol($currency) {
    return match ($currency) {
        'SEK' => 'kr',
        'NOK' => 'kr',
        'DKK' => 'kr',
        'EUR' => '€',
        'CAD' => 'C$',
        'NZD' => '$',
        'INR' => '₹',
        default => $currency,
    };
}

function getCurrentCurrencySymbol() {
    return getCurrencySymbol(CURRENT_CURRENCY);
}


define('AUTHORITIES', [
    "phoenix" => [
        "sv" => "SGA"
    ],
    "888" => [
        "en" => "KSA",
        "nl" => "KSA"
    ],
    "casinostugan"   => [
        "en" => "SGA",
        "sv" => "SGA",
    ],
    "casinostuen"    => [
        "en" => "DGA",
        "dk" => "DGA",
        "da" => "DGA"
    ],
    "cherrycasino"   => [
        "en"     => "MGA",
        "no"     => "MGA",
        "sv"     => "MGA"
    ],
    "comeon"         => [
        "en"     => "SGA",
        "ca"     => "AGCO",
        "on"     => "AGCO",
        "dk"     => "DGA",
        "da"     => "DGA",
        "fi"     => "MGA",
        "no"     => "MGA",
        "pl"     => "PGA",
        "sv"     => "SGA",
        "nl"     => "KSA"
    ],
    // 'comeon-white'   => ["in" => ""],
    "euroslots" => [
        "en"    => "MGA",
        "fi"    => "MGA",
    ],
    "folkeriket"     => [
        "en"     => "MGA",
        "no"     => "MGA",
    ],
    "galaksino"      => [
        "en" => "MGA",
        "fi" => "MGA",
    ],
    "getlucky"       => [
        "en"     => "MGA",
        "dk"     => "DGA",
        "da"     => "DGA",
        "fi"     => "MGA",
        "no"     => "MGA",
        "nl"     => "KSA",
        "sv"     => "SGA"
    ],
    "hajper"         => [
        "en" => "SGA",
        "sv" => "SGA",
    ],
    "lyllo"          => [
        "en" => "SGA",
        "sv" => "SGA",
    ],
    "mobilautomaten" => [
        "en"     => "MGA",
        "no"     => "MGA",
    ],
    "mobilebet"      => [
        "en"       => "GGA",
        "de"       => "GGA",
        "fi"       => "MGA",
        "no"       => "MGA",
    ],
    "nopeampi"       => [
        "en" => "MGA",
        "fi" => "MGA",
    ],
    "norgesspill"    => [
        "en"     => "MGA",
        "no"     => "MGA",
    ],
    "pzbuk"          => [
        "en" => "PGA",
        "pl" => "PGA",
    ],
    "snabbare"       => [
        "en" => "SGA",
        "sv" => "SGA",
    ],
    "sunmaker"       => [
        "en" => "GGA",
        "de" => "GGA",
    ],
    "suomikasino"    => [
        "en" => "MGA",
        "fi" => "MGA"
    ],
    "spinon" => [
        "en" => "MGA"
    ]
]);


// Gambling Authorities/Regulators based on Brand and Franchise
function getCurrentAuthority() {
    return AUTHORITIES[CURRENT_BRAND][CURRENT_REGION] ?? "";
}

// Parses the url, returns the root domain
function get_root_domain_recursively($host) {
    $domain = strtolower(trim((string) $host));
    $count = substr_count($domain, '.');
    if ($count === 2) {
        if (strlen(explode('.', $domain)[1]) > 3) $domain = explode('.', $domain, 2)[1];
    } else if ($count > 2) {
        $domain = get_root_domain_recursively(explode('.', $domain, 2)[1]);
    }
    return $domain;
}

function getCurrentPath() {
    if (defined('ICL_LANGUAGE_CODE')) {
        if (!empty(ICL_LANGUAGE_CODE)) {
            return "/" . ICL_LANGUAGE_CODE . "/";
        }
    }

    if (is_multisite()) {
        return get_blog_details(get_current_blog_id())->path;
    }

    return "/";
}

function is_login_page() {
    // true if login page URL is still normal
    if (in_array($GLOBALS['pagenow'], ['wp-login.php', 'wp-register.php'])) return true;

    // true if login page URL is changed by some security plugins
    if (has_action('login_init')) return true;

    // if nothing above happens >> return false
    return false;
}

function getCurrentDevice() {
    return $_SERVER['HTTP_X_DEVICE_TYPE'] ?? '';
}

function isCurrentDeviceMobile() {
    return CURRENT_DEVICE == 'mobile';
}

function isCurrentDeviceTablet() {
    return CURRENT_DEVICE == 'tablet';
}

function isCurrentDeviceDesktop() {
    return CURRENT_DEVICE == 'desktop';
}

function getCurrentDeviceScope() {
    return $_SERVER['HTTP_X_SCOPE'] ?? '';
}

function isCurrentDeviceAnApp() {
    return CURRENT_DEVICE_SCOPE == 'app';
}

function getCurrentDeviceOS() {
    return $_SERVER['HTTP_X_APP_VIEW'] ?? '';
}

function isCurrentDeviceAnIos() {
    return CURRENT_DEVICE_OS == 'ios';
}

function isCurrentDeviceAnAndroid() {
    return CURRENT_DEVICE_OS == 'android';
}


function isOrganic($url) {
    $organicList = [
        '360.cn', 'www.alice.com', 'aliceadsl.fr', 'www.alltheweb.com', 'www.altavista.com', 'www.aol.com', 'www.ask.com', 'search.aol.fr', 'alicesuche.aol.de', 'search.auone.jp', 'isearch.avg.com', 'search.babylon.com', 'www.baidu.com', 'biglobe.ne.jp', 'www.bing.com', 'search.centrum.cz', 'search.comcast.net', 'search.conduit.com', 'www.cnn.com/SEARCH', 'www.daum.net', 'duckduckgo.com', 'www.ecosia.org', 'www.ekolay.net', 'www.eniro.se', 'www.globo.com/busca', 'go.mail.ru', 'www.google', 'goo.ne.jp', 'www.haosou.com/s', 'search.incredimail.com', 'www.kvasir.no', 'www.bing.com', 'www.lycos.com', 'search.lycos.de', 'www.mamma.com', 'local.msn.com', 'money.msn.com', 'www.msn.com', 'www.mynet.com', 'najdi.si', 'www.naver.com', 'search.netscape.com', 'szukaj.onet.pl', 'www.ozu.es', 'rakuten.co.jp', 'rambler.ru', 'search-results.com', 'search.smt.docomo.ne.jp', 'sesam.no', 'www.seznam.cz', 'www.so.com/s', 'www.sogou.com', 'www.startsiden.no/sok', 'www.szukacz.pl', 'buscador.terra.com.br', 'search.tut.by', 'search.ukr.net', 'search.virgilio.it', 'www.voila.fr', 'www.wp.pl', 'www.yahoo.com', 'yahoo.cn', 'm.yahoo.com', 'www.yandex.com', 'yandex.ru'
    ];

    return in_array($url, $organicList);
}


// Affiliate IDs per brand (for organic)
define('BRAND_AFF', [
    'comeon' => 60604,
    'folkeautomaten' => 60606,
    'sagakingdom' => 60606,
    'mobilebet' => 60607,
    'casinostugan' => 60608,
    'getlucky' => 60609,
    'mobilautomaten' => 60610,
    'pzbuk' => 60611,
    'snabbare' => 60612,
    'nopeampi' => 60613,
    'hajper' => 60614,
    'blitzino' => 60615,
    'galaksino' => 60616,
    'lyllo' => 68877,
    'norgesspill' => 166599,
    'sunnyplayer' => 211154,
    'sunmaker' => 246264,
    'cherrycasino' => 269550,
    'kasynopl' => 736518,
    'euroslots' => 3175905,
]);